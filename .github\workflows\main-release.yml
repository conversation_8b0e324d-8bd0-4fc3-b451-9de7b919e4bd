# Main分支自动打包发布流程
# 当向main分支提交PR时触发多平台多架构的自动打包和发布
name: 自动打包发布

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
    types: [opened, synchronize, reopened]
  workflow_dispatch:
    inputs:
      version:
        description: '发布版本号 (例如: v1.0.0)'
        required: true
        default: 'v1.0.0'


# 设置GitHub Actions权限
permissions:
  contents: write  # 允许创建release和上传文件
  pages: write     # 允许部署到GitHub Pages
  id-token: write  # 允许OIDC token写入

jobs:
  # 发布前检查
  pre-release-check:
    name: 发布前检查
    runs-on: ubuntu-22.04  # 明确指定Ubuntu 22.04以避免Ubuntu 24.04兼容性问题

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: 运行完整测试
      run: |
        echo "🧪 运行发布前测试..."
        python scripts/run_tests.py
        pytest tests/ -v --cov=src

    - name: 检查PR构建配置
      run: |
        echo "📋 检查PR构建信息..."
        echo "PR编号: ${{ github.event.number }}"
        echo "源分支: ${{ github.head_ref }}"
        echo "目标分支: ${{ github.base_ref }}"
        echo "当前提交: ${{ github.sha }}"


        # 检查图标文件
        echo "🎨 检查应用图标文件..."
        ls -la src/resources/icons/

        # 验证PyInstaller环境
        echo "🔧 验证PyInstaller环境..."
        pip install pyinstaller pyinstaller-hooks-contrib
        python -c "import PyInstaller; print(f'PyInstaller: {PyInstaller.__version__}')"

  # Windows构建 - 生成单exe和目录压缩包
  build-windows:
    name: 构建Windows版本
    needs: pre-release-check
    runs-on: windows-latest

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        architecture: 'x64'

    - name: 安装Visual C++运行时和系统依赖
      run: |
        echo "🔧 安装Visual C++运行时和系统依赖..."

        # 安装Visual C++ Redistributable
        choco install vcredist2015 vcredist2017 vcredist2019 vcredist2022 -y

        # 安装Windows SDK组件（PyQt6需要）
        choco install windows-sdk-10-version-2004-all -y --ignore-checksums

        echo "✅ 系统依赖安装完成"

    - name: 安装Python依赖
      run: |
        python -m pip install --upgrade pip

        # 安装项目依赖（使用与本地相同的版本）
        pip install -r requirements.txt

        # 安装PyInstaller（使用与本地相同的版本）
        pip install pyinstaller==6.14.2 pyinstaller-hooks-contrib

    - name: 验证依赖安装
      run: |
        python -c "import PyQt6.QtCore; print('PyQt6.QtCore: OK')"
        python -c "import PyQt6.QtQml; print('PyQt6.QtQml: OK')"
        python -c "import PyInstaller; print(f'PyInstaller: {PyInstaller.__version__}')"

    - name: 构建单文件exe
      run: |
        echo "🔨 构建Windows单文件exe..."
        pyinstaller `
          --onefile `
          --windowed `
          --icon=src/resources/icons/app.ico `
          --name=EmailDomainManager `
          --collect-all PyQt6 `
          --add-data "src/resources;resources" `
          --add-data "src/views/qml;views/qml" `
          --paths src `
          src/main.py

        # 验证构建结果
        if (Test-Path "dist/EmailDomainManager.exe") {
          $size = (Get-Item "dist/EmailDomainManager.exe").Length / 1MB
          Write-Host "✅ 单文件exe构建成功，大小: $([math]::Round($size, 1))MB"
        } else {
          Write-Host "❌ 单文件exe构建失败"
          exit 1
        }

    - name: 构建目录版本
      run: |
        echo "🔨 构建Windows目录版本..."
        # 清理之前的构建
        Remove-Item -Recurse -Force build -ErrorAction SilentlyContinue

        pyinstaller `
          --onedir `
          --windowed `
          --icon=src/resources/icons/app.ico `
          --name=EmailDomainManager-Portable `
          --collect-all PyQt6 `
          --add-data "src/resources;resources" `
          --add-data "src/views/qml;views/qml" `
          --paths src `
          src/main.py

        # 验证构建结果
        if (Test-Path "dist/EmailDomainManager-Portable/EmailDomainManager-Portable.exe") {
          Write-Host "✅ 目录版本构建成功"
        } else {
          Write-Host "❌ 目录版本构建失败"
          exit 1
        }

    - name: 创建Windows发布包
      run: |
        echo "📦 创建Windows发布包..."

        # 创建发布目录
        New-Item -ItemType Directory -Force -Path "release"

        # 1. 单文件exe
        Copy-Item "dist/EmailDomainManager.exe" "release/EmailDomainManager-Windows-x64.exe"

        # 2. 便携版zip
        $compress = @{
          Path = "dist/EmailDomainManager-Portable/*"
          CompressionLevel = "Optimal"
          DestinationPath = "release/EmailDomainManager-Windows-x64-Portable.zip"
        }
        Compress-Archive @compress

        # 显示结果
        Write-Host "📁 Windows发布文件:"
        Get-ChildItem "release" | ForEach-Object {
          $size = $_.Length / 1MB
          Write-Host "  $($_.Name): $([math]::Round($size, 1))MB"
        }

    - name: 上传Windows构建产物
      uses: actions/upload-artifact@v4
      with:
        name: windows-builds
        path: |
          release/EmailDomainManager-Windows-x64.exe
          release/EmailDomainManager-Windows-x64-Portable.zip
        retention-days: 30

  # Ubuntu构建 - 生成可执行文件和tar.gz包
  build-ubuntu:
    name: 构建Ubuntu版本
    needs: pre-release-check
    runs-on: ubuntu-22.04

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: 安装系统依赖
      run: |
        echo "📦 安装Ubuntu系统依赖..."
        sudo apt-get update

        # 安装PyQt6运行时依赖
        sudo apt-get install -y \
          libgl1-mesa-glx \
          libgl1-mesa-dev \
          libglib2.0-0 \
          libglib2.0-dev \
          libxkbcommon-x11-0 \
          libxkbcommon-dev \
          libxcb-icccm4 \
          libxcb-image0 \
          libxcb-keysyms1 \
          libxcb-randr0 \
          libxcb-render-util0 \
          libxcb-xinerama0 \
          libxcb-xfixes0 \
          libxcb-shape0 \
          libxcb-sync1 \
          libxcb-shm0 \
          libxcb-util1 \
          libxcb-cursor0 \
          libfontconfig1 \
          libfontconfig1-dev \
          libfreetype6 \
          libfreetype6-dev \
          libx11-6 \
          libx11-dev \
          libx11-xcb1 \
          libxext6 \
          libxext-dev \
          libxrender1 \
          libxrender-dev \
          libegl1-mesa \
          libegl1-mesa-dev \
          libxss1 \
          libasound2 \
          libasound2-dev \
          libpulse0 \
          libpulse-dev \
          libdrm2 \
          libdrm-dev \
          libxcomposite1 \
          libxcomposite-dev \
          libxdamage1 \
          libxdamage-dev \
          libxrandr2 \
          libxrandr-dev \
          libxtst6 \
          libxtst-dev

        echo "✅ 系统依赖安装完成"

    - name: 安装Python依赖
      run: |
        echo "🐍 安装Python依赖..."
        python -m pip install --upgrade pip

        # Ubuntu特殊处理：先安装兼容的PyQt6版本
        echo "🐧 Ubuntu平台：安装兼容的PyQt6版本..."
        pip install PyQt6==6.6.1 PyQt6-Qt6==6.6.1 PyQt6-sip==13.6.0

        # 安装其他项目依赖（跳过PyQt6相关）
        pip install -r requirements.txt --no-deps
        pip install requests aiohttp loguru pydantic cryptography asyncqt

        # 安装PyInstaller
        pip install pyinstaller==6.14.2 pyinstaller-hooks-contrib

        echo "✅ Python依赖安装完成"

    - name: 验证依赖安装
      run: |
        echo "🔍 验证依赖安装..."
        python -c "import PyQt6.QtCore; print('PyQt6.QtCore: OK')"
        python -c "from PyQt6.QtCore import qVersion; print(f'Qt6 version: {qVersion()}')"
        python -c "import PyInstaller; print(f'PyInstaller: {PyInstaller.__version__}')"
        echo "✅ 核心依赖验证通过"

    - name: 构建Ubuntu可执行文件
      run: |
        echo "🔨 构建Ubuntu可执行文件..."
        pyinstaller \
          --onedir \
          --windowed \
          --icon=src/resources/icons/app.png \
          --name=EmailDomainManager \
          --collect-all PyQt6 \
          --add-data "src/resources:resources" \
          --add-data "src/views/qml:views/qml" \
          --paths src \
          src/main.py

        # 验证构建结果
        if [ -f "dist/EmailDomainManager/EmailDomainManager" ]; then
          chmod +x dist/EmailDomainManager/EmailDomainManager
          size=$(stat -c%s "dist/EmailDomainManager/EmailDomainManager")
          size_mb=$((size / 1024 / 1024))
          echo "✅ Ubuntu可执行文件构建成功，大小: ${size_mb}MB"

          # 检查文件信息
          file dist/EmailDomainManager/EmailDomainManager
          echo "📁 构建目录内容:"
          ls -la dist/EmailDomainManager/
        else
          echo "❌ Ubuntu可执行文件构建失败"
          echo "📁 dist目录内容:"
          ls -la dist/ || echo "dist目录不存在"
          exit 1
        fi

    - name: 创建Ubuntu发布包
      run: |
        echo "📦 创建Ubuntu发布包..."

        # 创建发布目录
        mkdir -p release

        # 验证构建结果
        if [ ! -d "dist/EmailDomainManager" ]; then
          echo "❌ Ubuntu构建目录不存在"
          exit 1
        fi

        # 创建安装包目录
        mkdir -p EmailDomainManager-Ubuntu-x64
        cp -r dist/EmailDomainManager/* EmailDomainManager-Ubuntu-x64/

        # 创建启动脚本
        cat > EmailDomainManager-Ubuntu-x64/run.sh << 'EOF'
        #!/bin/bash
        # 域名邮箱管理器启动脚本

        # 获取脚本所在目录
        DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

        # 运行程序
        cd "$DIR"
        ./EmailDomainManager "$@"
        EOF

        chmod +x EmailDomainManager-Ubuntu-x64/run.sh
        chmod +x EmailDomainManager-Ubuntu-x64/EmailDomainManager

        # 创建README
        cat > EmailDomainManager-Ubuntu-x64/README.txt << 'EOF'
        域名邮箱管理器 Ubuntu版本

        安装说明:
        1. 解压文件: tar -xzf EmailDomainManager-Ubuntu-x64.tar.gz
        2. 进入目录: cd EmailDomainManager-Ubuntu-x64
        3. 运行程序: ./EmailDomainManager 或 ./run.sh

        系统要求:
        - Ubuntu 18.04+ 或其他现代Linux发行版 (x86_64)
        - 4GB+ RAM (推荐)
        - 100MB+ 磁盘空间
        - X11或Wayland显示服务器

        如遇到依赖问题，请安装:
        sudo apt install libgl1-mesa-glx libglib2.0-0 libxkbcommon-x11-0 libfontconfig1 libfreetype6

        项目主页: https://github.com/your-username/email-domain-manager
        EOF

        # 创建tar.gz包
        tar -czf release/EmailDomainManager-Ubuntu-x64.tar.gz EmailDomainManager-Ubuntu-x64/

        # 显示结果
        echo "📁 Ubuntu发布文件:"
        ls -lh release/

        echo "✅ Ubuntu发布包创建完成"

    - name: 上传Ubuntu构建产物
      uses: actions/upload-artifact@v4
      with:
        name: ubuntu-builds
        path: release/EmailDomainManager-Ubuntu-x64.tar.gz
        retention-days: 30


  # 创建GitHub Release
  create-release:
    name: 创建GitHub Release
    needs: [build-windows, build-ubuntu]
    runs-on: ubuntu-22.04
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch' || github.event_name == 'pull_request'

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 下载Windows构建产物
      uses: actions/download-artifact@v4
      with:
        name: windows-builds
        path: artifacts/windows/

    - name: 下载Ubuntu构建产物
      uses: actions/download-artifact@v4
      with:
        name: ubuntu-builds
        path: artifacts/ubuntu/

    - name: 整理构建产物
      run: |
        echo "📁 整理构建产物..."

        # 创建发布目录
        mkdir -p release-files

        # 复制Windows构建产物
        if [ -d "artifacts/windows" ]; then
          cp artifacts/windows/* release-files/
        fi

        # 复制Ubuntu构建产物
        if [ -d "artifacts/ubuntu" ]; then
          cp artifacts/ubuntu/* release-files/
        fi

        echo "📦 最终发布文件:"
        ls -lh release-files/

    - name: 生成发布说明
      run: |
        echo "📝 生成发布说明..."

        # 获取最新的提交信息作为发布说明
        RELEASE_NOTES=$(git log --pretty=format:"- %s" -10)

        # 根据事件类型设置发布类型标识
        if [[ "${{ github.event_name }}" == "pull_request" ]]; then
          RELEASE_TYPE="🧪 测试版本 (PR #${{ github.event.number }})"
          RELEASE_TITLE="域名邮箱管理器 测试版本"
        else
          RELEASE_TYPE="🎉 正式发布版本"
          RELEASE_TITLE="域名邮箱管理器 多平台发布版本"
        fi

        cat > release-notes.md << EOF
        # $RELEASE_TITLE

        ## $RELEASE_TYPE

        ## 🎉 新功能和改进

        $RELEASE_NOTES

        ## 📦 下载说明

        ### Windows版本 (x86_64)
        - **单文件exe**: \`EmailDomainManager-Windows-x64.exe\` - 双击即可运行，无需安装，启动较慢
        - **便携版**: \`EmailDomainManager-Windows-x64-Portable.zip\` - 解压后运行，启动更快，推荐

        ### Ubuntu版本 (x86_64)
        - **完整包**: \`EmailDomainManager-Ubuntu-x64.tar.gz\` - 包含可执行文件和说明文档

        ## 🚀 安装说明

        ### Windows用户
        **方式1 - 便携版 (推荐)**:
        1. 下载 \`EmailDomainManager-Windows-x64-Portable.zip\`
        2. 解压到任意目录
        3. 运行 \`EmailDomainManager-Portable.exe\`

        **方式2 - 单文件exe**:
        1. 下载 \`EmailDomainManager-Windows-x64.exe\`
        2. 双击exe文件直接运行，无需安装（启动较慢）

        ### Ubuntu用户
        1. 下载 \`EmailDomainManager-Ubuntu-x64.tar.gz\`
        2. 解压: \`tar -xzf EmailDomainManager-Ubuntu-x64.tar.gz\`
        3. 进入目录: \`cd EmailDomainManager-Ubuntu-x64\`
        4. 运行: \`./EmailDomainManager\` 或 \`./run.sh\`

        ## 📋 系统要求

        ### Windows
        - Windows 10+ (x86_64)
        - 4GB+ RAM (推荐)
        - 100MB+ 磁盘空间

        ### Ubuntu/Linux
        - Ubuntu 18.04+ 或其他现代Linux发行版 (x86_64)
        - 4GB+ RAM (推荐)
        - 100MB+ 磁盘空间
        - X11或Wayland显示服务器
        - 如遇依赖问题: \`sudo apt install libgl1-mesa-glx libglib2.0-0 libxkbcommon-x11-0\`

        ## 🎨 应用特性

        - 基于PyQt6的现代化界面
        - Material Design设计风格
        - 跨平台支持 (Windows/Linux)
        - 完整的邮箱管理功能
        - 单文件exe和便携版双重选择

        ## 🐛 问题反馈

        如有问题请在 [Issues](https://github.com/your-username/email-domain-manager/issues) 中反馈。
        EOF

    - name: 确定版本号
      id: version
      run: |
        if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
          VERSION="${{ github.event.inputs.version }}"
        elif [[ "${{ github.event_name }}" == "pull_request" ]]; then
          # PR测试版本：包含PR编号
          VERSION="v$(date +'%Y.%m.%d')-pr${{ github.event.number }}-test${{ github.run_number }}"
        else
          # 自动生成版本号：v年.月.日-构建号
          VERSION="v$(date +'%Y.%m.%d')-${{ github.run_number }}"
        fi
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "发布版本: $VERSION"

    - name: 创建Release
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ steps.version.outputs.version }}
        name: 域名邮箱管理器 ${{ steps.version.outputs.version }}
        body_path: release-notes.md
        draft: false
        prerelease: ${{ github.event_name == 'pull_request' }}
        files: |
          release-files/EmailDomainManager-Windows-x64.exe
          release-files/EmailDomainManager-Windows-x64-Portable.zip
          release-files/EmailDomainManager-Ubuntu-x64.tar.gz
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # 部署文档 (临时允许PR触发，用于测试)
  deploy-docs:
    name: 部署文档
    needs: create-release
    runs-on: ubuntu-22.04  # 明确指定Ubuntu 22.04
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch' || github.event_name == 'pull_request'

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Python环境
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: 安装文档工具
      run: |
        python -m pip install --upgrade pip
        pip install mkdocs mkdocs-material

    - name: 构建文档
      run: |
        echo "📚 构建项目文档..."

        # 创建mkdocs配置
        cat > mkdocs.yml << EOF
        site_name: 域名邮箱管理器
        site_description: 基于PyQt6的域名邮箱管理工具 - 多平台自动打包发布

        nav:
          - 首页: README.md
          - 下载安装: docs/installation.md
          - 使用指南: docs/user-guide.md
          - 开发文档: docs/development.md
          - API规范: docs/api-specification.md
          - 更新日志: docs/changelog.md

        theme:
          name: material
          language: zh
          palette:
            - scheme: default
              primary: blue
              accent: blue
              toggle:
                icon: material/brightness-7
                name: 切换到深色模式
            - scheme: slate
              primary: blue
              accent: blue
              toggle:
                icon: material/brightness-4
                name: 切换到浅色模式
          features:
            - navigation.tabs
            - navigation.sections
            - navigation.expand
            - navigation.top
            - search.highlight
            - search.share
            - content.code.copy

        markdown_extensions:
          - pymdownx.highlight
          - pymdownx.superfences
          - pymdownx.tabbed
          - admonition
          - pymdownx.details
        EOF

        # 创建基础文档结构
        mkdir -p docs

        # 创建安装文档
        cat > docs/installation.md << EOF
        # 下载与安装

        ## 支持的平台

        - Windows x86_64
        - Linux x86_64

        ## 下载地址

        请访问 [GitHub Releases](https://github.com/your-username/email-domain-manager/releases) 下载最新版本。

        ## 安装说明

        ### Windows
        1. 下载 \`EmailDomainManager-windows-x86_64.zip\`
        2. 解压到任意目录
        3. 双击 \`EmailDomainManager.exe\` 运行

        ### Linux
        1. 下载 \`EmailDomainManager-linux-x86_64.tar.gz\`
        2. 解压并运行
        EOF

        mkdocs build

    - name: 部署到GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./site

  # 构建通知和总结
  build-notification:
    name: 构建通知
    needs: [build-windows, build-ubuntu, create-release, deploy-docs]
    runs-on: ubuntu-22.04
    if: always()

    steps:
    - name: 构建结果通知
      run: |
        if [[ "${{ github.event_name }}" == "pull_request" ]]; then
          echo "🔍 PR构建测试和预发布完成"
          echo "========================"
          echo "PR信息:"
          echo "  - PR编号: #${{ github.event.number }}"
          echo "  - 源分支: ${{ github.head_ref }}"
          echo "  - 目标分支: ${{ github.base_ref }}"
          echo "  - 测试版本: ${{ needs.create-release.outputs.version || '未知' }}"
        else
          echo "🎉 正式发布流程完成"
          echo "========================"
        fi

        # 检查构建任务结果
        echo "📋 任务执行结果:"
        echo "  - Windows构建: ${{ needs.build-windows.result }}"
        echo "  - Ubuntu构建: ${{ needs.build-ubuntu.result }}"

        if [[ "${{ github.event_name }}" == "push" ]] || [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
          echo "  - 创建Release: ${{ needs.create-release.result }}"
          echo "  - 部署文档: ${{ needs.deploy-docs.result }}"
        fi

        if [[ "${{ contains(needs.*.result, 'failure') }}" == "true" ]]; then
          echo ""
          echo "❌ 流程中出现问题"
          echo "失败的任务:"
          [[ "${{ needs.build-windows.result }}" == "failure" ]] && echo "  - Windows构建失败"
          [[ "${{ needs.build-ubuntu.result }}" == "failure" ]] && echo "  - Ubuntu构建失败"
          [[ "${{ needs.create-release.result }}" == "failure" ]] && echo "  - 创建Release失败"
          [[ "${{ needs.deploy-docs.result }}" == "failure" ]] && echo "  - 部署文档失败"
          echo ""
          echo "请检查上述失败任务的日志以获取详细错误信息。"
        else
          echo ""
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            echo "✅ PR构建测试和预发布成功！"
            echo "📦 Windows和Ubuntu平台构建成功"
            echo "🎯 已创建测试版本Release，可以下载测试"
            echo "✅ 测试通过后可以安全合并到main分支"
          else
            echo "🎉 正式发布成功完成！"
            echo "📦 新版本已发布到GitHub Releases"
            echo "📚 文档已更新到GitHub Pages"
            echo ""
            echo "🔗 相关链接:"
            echo "  - Releases: https://github.com/${{ github.repository }}/releases"
            echo "  - 文档: https://${{ github.repository_owner }}.github.io/${{ github.event.repository.name }}"
          fi
        fi

        echo ""
        echo "📊 构建统计:"
        echo "  - Windows: 单文件exe + 便携版zip"
        echo "  - Ubuntu: 单文件 + 完整tar.gz包"
        echo "  - 构建时间: $(date)"
        echo "  - PR编号: #${{ github.event.number }}"
        echo "  - 源分支: ${{ github.head_ref }}"
        echo "  - 提交SHA: ${{ github.sha }}"
